<template>
  <div>
    <div class="header-wrapper">
      <div class="header">
        <div class="logo-section">
          <router-link to="/" class="logo-link">
            <span class="blue-text">
              <img src="@/assets/images/ordos-logo.png" alt="logo" style="width: 32px; height: 32px;">
            </span>
            <span style="margin-top: -4px; margin-left: 10px;">鄂尔多斯市精神文明建设服务中心智能知识库</span>
          </router-link>
        </div>
        <div class="nav-tabs">
          <div v-for="(item, index) in filteredNavItems" :key="index" class="nav-item-wrapper">
            <router-link :to="item.path" class="nav-tab" :exact="item.exact" active-class="active">
              <v-icon size="22">{{ item.icon }}</v-icon>
              <span>{{ item.title }}</span>
            </router-link>
          </div>
        </div>

        <!-- <div class="search-box">
          <v-icon class="search-icon">mdi-magnify</v-icon>
          <input type="text" placeholder="搜索" v-model="searchText" @keyup.enter="handleSearch" />
        </div> -->

        <div class="user-section">
          <!-- <span class="username">{{ store.name }}</span> -->
          <button class="logout-btn" @click="handleLogout">
            <v-icon>mdi-logout</v-icon>
            <span>退出</span>
          </button>
          <div class="user-avatar" @click="showUserInfo"
            :style="!userInfo.avatar ? { background: getAvatarBackground } : {}">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="avatar" />
            <span v-else class="avatar-text">{{ userInfo.nickName ? userInfo.nickName.charAt(0) : '' }}</span>
          </div>
        </div>
      </div>
    </div>
    <UserInfoPopup ref="userInfoPopupRef" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, watch } from 'vue';
import { useRouter, onBeforeRouteUpdate, useRoute } from 'vue-router';
import useUserStore from '@/store/modules/user';
import { ElMessageBox, ElMessage } from 'element-plus';
import { logoutApi } from '@/api/user';
import { removeToken, removeCaptchaType, removeIsCode, getToken } from '@/utils/auth';
import auth from '@/plugins/auth';
import UserInfoPopup from './UserInfoPopup.vue';
import { getAvatarColor } from '@/utils/avatarColor';

const router = useRouter();
const route = useRoute();
const store = useUserStore();
const showProjectDropdown = ref(false);
const selectedProject = ref('上海金融中心智能化项目');
const permissions = ref([]);
const userInfoPopupRef = ref(null);
const searchText = ref('');
const userInfo = ref({});

// 项目列表数据
const projectList = [
  '上海金融中心智能化项目',
  '北京中心大厦智能化项目',
  '广州城投大厦智能化项目',
  '深圳地铁大厦智能化项目'
];

// 选择项目的处理函数
const selectProject = (project) => {
  selectedProject.value = project;
  showProjectDropdown.value = false;
};

// 权限检查函数
const checkPermission = (permission) => {
  // 如果没有设置权限要求，始终显示
  if (!permission) return true;

  // 如果权限是数组，使用auth插件的hasPermiOr方法
  if (Array.isArray(permission)) {
    return auth.hasPermiOr(permission);
  }

  // 如果权限是字符串，使用auth插件的hasPermi方法
  return auth.hasPermi(permission);
};

// 导航项数据
const navItems = [
  { title: '智能问答', path: '/chat', icon: 'mdi-chart-donut', exact: true, permission: ['ai:knowledge:cockpit'] },
  { title: '知识库', path: '/chat-file', icon: 'mdi-home', exact: true, permission: ['ai:knowledge:cockpit'] },
  { title: '人员管理', path: '/manage/user', icon: 'mdi-cube', exact: false, permission: 'ai:knowledge:userManage' },
  // { title: '智能问答', path: '/chat', icon: 'mdi-message-text', exact: false, permission: ['ai:knowledge:answer'] },
  // // { title: '知识图谱', path: '/library', icon: 'mdi-graph', exact: false },
  // { title: '智能文档', path: '/documents', icon: 'mdi-file-document', exact: false, permission: 'ai:knowledge:file' },
  // // { title: '运维数据', path: '/operation', icon: 'mdi-database', exact: false },
  // { title: '用户管理', path: '/system/user', icon: 'mdi-account', exact: false, permission: 'system:user:list' },
  // { title: '角色管理', path: '/system/role', icon: 'mdi-account-group', exact: false, permission: 'system:role:list' },
  // { title: '部门管理', path: '/system/dept', icon: 'mdi-domain', exact: false, permission: 'system:dept:list' },
  // { title: '菜单管理', path: '/system/menu', icon: 'mdi-menu', exact: false, permission: 'system:menu:list' },
];

// 根据权限过滤导航项
const filteredNavItems = computed(() => {
  // 遍历导航项并检查权限
  return navItems.filter(item => checkPermission(item.permission));
});

// 同步加载权限数据
const loadPermissions = async () => {
  // 直接使用store中的权限
  permissions.value = store.permissions || [];

  // 如果store中没有权限数据，尝试从服务器获取
  if (!permissions.value.length) {
    try {
      await store.getInfo();
      permissions.value = store.permissions || [];
      console.log('从服务器获取的权限:', permissions.value);
    } catch (error) {
      console.error('从服务器获取权限数据出错:', error);

      // 尝试从localStorage获取
      try {
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
          const userInfoObj = JSON.parse(userInfo);
          if (userInfoObj && userInfoObj.permissions && userInfoObj.permissions.length) {
            permissions.value = userInfoObj.permissions;
            // 同步回store
            store.setPermissions(permissions.value);
            console.log('从localStorage获取的权限:', permissions.value);
            return;
          }
        }
      } catch (error) {
        console.error('从localStorage解析权限数据出错:', error);
      }

      // 最后尝试从sessionStorage获取
      try {
        const userInfo = sessionStorage.getItem('userInfo');
        if (userInfo) {
          const userInfoObj = JSON.parse(userInfo);
          if (userInfoObj && userInfoObj.permissions && userInfoObj.permissions.length) {
            permissions.value = userInfoObj.permissions;
            // 同步回store
            store.setPermissions(permissions.value);
            console.log('从sessionStorage获取的权限:', permissions.value);
            return;
          }
        }
      } catch (error) {
        console.error('从sessionStorage解析权限数据出错:', error);
      }
    }
  }

  // 权限为空时检查是否已登录，未登录则跳转到登录页
  if (!permissions.value.length) {
    const token = getToken();
    if (!token) {
      console.log('未检测到登录状态，准备跳转至登录页');
      // 确保不在登录页才进行跳转
      if (router.currentRoute.value.path !== '/login') {
        router.push('/login');
      }
    } else {
      console.log('检测到登录状态，但未获取到权限数据');

      // 检查当前路由是否需要权限
      const path = router.currentRoute.value.path;
      const navItem = navItems.find(item => item.path === path);

      if (navItem && navItem.permission) {
        // 如果当前路由需要权限但用户没有，跳转到401
        console.warn('用户没有访问当前页面的权限');
        ElMessage.error('没有权限访问该页面');
        router.push('/401');
      }
    }
  }
};

// 监听路由变化
watch(() => route.path, async (newPath) => {
  console.log('路由变化，检查权限:', newPath);
  await loadPermissions();

  // 检查当前路由是否有权限访问
  const navItem = navItems.find(item => item.path === newPath);
  if (navItem && navItem.permission && !checkPermission(navItem.permission)) {
    console.warn('用户没有访问当前页面的权限');
    ElMessage.error('没有权限访问该页面');
    router.push('/401');
  }
});

// 监听权限变化
watch(() => store.permissions, (newPermissions) => {
  if (newPermissions && newPermissions.length) {
    permissions.value = newPermissions;
    // console.log('权限更新:', permissions.value);
  }
});

// 获取用户头像背景色
const getAvatarBackground = computed(() => {
  return userInfo.value && userInfo.value.userId ?
    getAvatarColor(userInfo.value.userId) :
    getAvatarColor('default');
});

// 更新用户信息
const updateUserInfo = async () => {
  console.log('开始更新用户信息');
  try {
    // 从store重新获取最新的用户信息
    await store.getInfo();
    userInfo.value = store.users || {};
    console.log('用户信息已更新:', userInfo.value);
  } catch (error) {
    console.error('更新用户信息失败:', error);
  }
};

// 当组件加载时加载权限数据
onMounted(async () => {
  // console.log('组件加载，开始获取权限');
  await loadPermissions();
  setup401Listener();

  // 获取用户信息
  userInfo.value = store.users || {};

  // 检查当前路由是否有权限访问
  const currentPath = router.currentRoute.value.path;
  const navItem = navItems.find(item => item.path === currentPath);
  if (navItem && navItem.permission && !checkPermission(navItem.permission)) {
    // console.warn('用户没有访问当前页面的权限');
    ElMessage.error('没有权限访问该页面');
    router.push('/401');
  }

  // console.log('最终加载的权限列表:', permissions.value);
  // console.log('过滤后的导航项:', filteredNavItems.value);

  // 监听用户信息更新事件
  window.addEventListener('userInfoUpdated', updateUserInfo);
});

// 处理401未授权响应
const handle401 = () => {
  // console.log('检测到401未授权响应，清除权限数据');
  // 清空权限，确保导航项不显示
  permissions.value = [];
  store.permissions = [];

  // 如果当前不在401页面，才跳转
  if (router.currentRoute.value.path !== '/401') {
    router.push('/401');
  }
};

// 全局事件监听器，监听401事件
const setup401Listener = () => {
  window.addEventListener('unauthorized', handle401);
};

// 清理事件监听器
const cleanup401Listener = () => {
  window.removeEventListener('unauthorized', handle401);
};

// 组件卸载时清理监听器
onUnmounted(() => {
  cleanup401Listener();
  // 移除用户信息更新监听器
  window.removeEventListener('userInfoUpdated', updateUserInfo);
});

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用退出接口
      await logoutApi();

      // 强制清空所有缓存
      removeToken();
      removeCaptchaType();
      removeIsCode();
      localStorage.clear();
      sessionStorage.clear();

      // 重置 store 状态
      store.$reset();

      // 直接跳转到登录页
      router.push('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      ElMessage.error('退出登录失败，请重试');
    }
  }).catch(() => { });
};

// 显示用户信息弹窗
const showUserInfo = () => {
  // 确保用户信息是最新的
  userInfo.value = store.users || {};
  userInfoPopupRef.value?.open();
};

// 处理搜索
const handleSearch = () => {
  if (searchText.value.trim()) {
    console.log('搜索:', searchText.value);
    // 这里可以添加搜索逻辑
    // 例如: router.push({ path: '/search', query: { q: searchText.value } });
  }
};
</script>

<style scoped>
.header-wrapper {
  width: 100%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: var(--card-background);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.header {
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 28px;
  position: relative;
  box-sizing: border-box;
}

.logo-section {
  flex-shrink: 0;
}

.logo-link {
  font-weight: 700;
  font-size: 20px;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.5px;
  display: flex;
  align-items: center;
}

.blue-text {
  color: #2196f3;
  background: linear-gradient(45deg, #2196f3, #1e88e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.blue-icon {
  color: #1e88e5 !important;
}

.project-selector-wrapper {
  margin-right: 12px;
  flex-shrink: 0;
}

.project-selector-btn {
  height: 40px;
  text-transform: none !important;
  letter-spacing: normal !important;
  font-weight: normal !important;
}

.dropdown-item-custom {
  min-height: 44px !important;
  padding: 4px 16px !important;
}

.selector-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-size: 14px;
}

.nav-tabs {
  display: flex;
  flex: 1;
  margin-left: 24px;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  height: 64px;
  gap: 10px;
}

.nav-item-wrapper {
  position: relative;
  min-width: 100px;
  height: 64px;
  display: flex;
  box-sizing: border-box;
  flex: 0 0 auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 18px;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 15px;
  white-space: nowrap;
  box-sizing: border-box;
  border-bottom: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  position: relative;
}

.nav-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: #2196f3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
  border-radius: 3px 3px 0 0;
}

.nav-tab:hover::after,
.nav-tab.active::after {
  width: 80%;
}

.nav-tab .v-icon {
  margin-right: 8px;
  flex-shrink: 0;
  color: var(--text-secondary);
  transition: all 0.3s;
  width: 22px;
  text-align: center;
}

.nav-tab:hover {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.05);
}

.nav-tab:hover .v-icon {
  color: #2196f3;
  transform: scale(1.1);
}

.nav-tab.active {
  color: #2196f3;
  font-weight: 600;
}

.nav-tab.active .v-icon {
  color: #2196f3;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.9);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 14px;
  margin-left: auto;
  height: 38px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 240px;
  position: relative;
}

.search-box:hover {
  border-color: #bdbdbd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.search-box:focus-within {
  border-color: #2196f3;
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.25);
}

.search-icon {
  color: #757575;
  font-size: 18px !important;
  margin-right: 8px;
  flex-shrink: 0;
}

.search-box:focus-within .search-icon {
  color: #2196f3;
}

.search-box input {
  border: none;
  background: transparent;
  outline: none;
  flex: 1;
  font-size: 14px;
  color: #333;
  cursor: text;
  padding: 0;
}

.search-box input::placeholder {
  color: #9e9e9e;
  opacity: 1;
}

.user-section {
  display: flex;
  align-items: center;
  margin-left: 20px;
  flex-shrink: 0;
  gap: 18px;
}

.username {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.logout-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logout-btn:hover {
  background-color: rgba(244, 67, 54, 0.08);
  transform: translateY(-1px);
}

.logout-btn .v-icon {
  margin-right: 6px;
  font-size: 18px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logout-btn:hover .v-icon {
  transform: translateX(-2px);
}

.user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #2196f3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
}

@media (max-width: 1024px) {
  .nav-tabs {
    display: none;
  }

  .project-selector-wrapper {
    flex: 1;
  }

  .search-box {
    width: 180px;
  }

  .header {
    height: 60px;
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .user-section {
    gap: 12px;
  }

  .logout-btn {
    padding: 6px 12px;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 600px) {
  .project-selector-wrapper {
    display: none;
  }

  .search-box {
    width: 140px;
  }

  .logo-section {
    margin-right: 0;
  }

  .logo-link {
    font-size: 18px;
  }

  .header {
    padding: 0 16px;
  }
}

/* 确保智能问答项的宽度固定 */
.nav-item-wrapper:nth-child(2) {
  min-width: 115px;
}
</style>